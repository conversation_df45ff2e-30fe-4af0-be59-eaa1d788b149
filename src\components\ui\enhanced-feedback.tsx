'use client';

import React, { memo, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';
import { TextAnimate } from './text-animate';
import { ShimmerButton } from './shimmer-button';
import { Particles } from './particles';

interface FeedbackToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  onClose: (id: string) => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const FeedbackToast = memo(({ 
  id, 
  type, 
  title, 
  message, 
  duration = 5000, 
  onClose, 
  action 
}: Readonly<FeedbackToastProps>) => {
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev - (100 / (duration / 100));
        if (newProgress <= 0) {
          onClose(id);
          return 0;
        }
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [duration, id, onClose]);

  const getConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: '✅',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          textColor: 'text-green-800 dark:text-green-200',
          gradientColor: '#10B981',
          progressColor: 'bg-green-500'
        };
      case 'error':
        return {
          icon: '❌',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-200',
          gradientColor: '#EF4444',
          progressColor: 'bg-red-500'
        };
      case 'warning':
        return {
          icon: '⚠️',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          textColor: 'text-yellow-800 dark:text-yellow-200',
          gradientColor: '#F59E0B',
          progressColor: 'bg-yellow-500'
        };
      default:
        return {
          icon: 'ℹ️',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          textColor: 'text-blue-800 dark:text-blue-200',
          gradientColor: '#3B82F6',
          progressColor: 'bg-blue-500'
        };
    }
  };

  const config = getConfig();

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: -50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.9 }}
      whileHover={{ scale: 1.02 }}
      className="relative w-full max-w-md"
    >
      <MagicCard 
        className={`p-4 ${config.bgColor} border ${config.borderColor} shadow-lg`}
        gradientColor={config.gradientColor}
        gradientSize={200}
      >
        <div className="flex items-start space-x-3">
          <motion.div 
            className="text-2xl flex-shrink-0"
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          >
            {config.icon}
          </motion.div>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <TextAnimate
                  animation="blurInUp"
                  className={`font-semibold ${config.textColor} text-sm`}
                >
                  {title}
                </TextAnimate>
                <p className={`${config.textColor} text-xs mt-1 opacity-80`}>
                  {message}
                </p>
              </div>

              <motion.button
                onClick={() => onClose(id)}
                className={`${config.textColor} opacity-50 hover:opacity-100 transition-opacity ml-2`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                ✕
              </motion.button>
            </div>

            {action && (
              <motion.div 
                className="mt-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <ShimmerButton 
                  onClick={action.onClick}
                  className="text-xs px-3 py-1"
                  shimmerColor={config.gradientColor}
                  background={`${config.gradientColor}80`}
                >
                  {action.label}
                </ShimmerButton>
              </motion.div>
            )}
          </div>
        </div>

        {/* Barra de progresso */}
        <motion.div 
          className="mt-3 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <motion.div 
            className={`h-full ${config.progressColor} rounded-full`}
            style={{ width: `${progress}%` }}
            transition={{ duration: 0.1, ease: "linear" }}
          />
        </motion.div>

        {/* Efeito de partículas para sucesso */}
        {type === 'success' && (
          <div className="absolute inset-0 pointer-events-none">
            <Particles
              className="absolute inset-0"
              quantity={20}
              ease={80}
              color="#10B981"
              refresh
            />
          </div>
        )}
      </MagicCard>
    </motion.div>
  );
});

FeedbackToast.displayName = 'FeedbackToast';

interface FeedbackProviderProps {
  children: React.ReactNode;
}

interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Context para gerenciar toasts globalmente
const FeedbackContext = React.createContext<{
  showToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
} | null>(null);

export const FeedbackProvider = memo(({ children }: Readonly<FeedbackProviderProps>) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const showToast = React.useCallback((toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { ...toast, id }]);
  }, []);

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  return (
    <FeedbackContext.Provider value={{ showToast, removeToast }}>
      {children}
      
      {/* Container de toasts */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        <AnimatePresence mode="popLayout">
          {toasts.map((toast) => (
            <FeedbackToast
              key={toast.id}
              {...toast}
              onClose={removeToast}
            />
          ))}
        </AnimatePresence>
      </div>
    </FeedbackContext.Provider>
  );
});

FeedbackProvider.displayName = 'FeedbackProvider';

// Hook para usar o sistema de feedback
export const useFeedback = () => {
  const context = React.useContext(FeedbackContext);
  if (!context) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
};

// Componente de confirmação modal
interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
}

export const ConfirmDialog = memo(({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  type = 'info'
}: Readonly<ConfirmDialogProps>) => {
  const getConfig = () => {
    switch (type) {
      case 'danger':
        return {
          icon: '⚠️',
          gradientColor: '#EF4444',
          confirmColor: 'bg-red-600 hover:bg-red-700'
        };
      case 'warning':
        return {
          icon: '⚠️',
          gradientColor: '#F59E0B',
          confirmColor: 'bg-yellow-600 hover:bg-yellow-700'
        };
      default:
        return {
          icon: 'ℹ️',
          gradientColor: '#3B82F6',
          confirmColor: 'bg-blue-600 hover:bg-blue-700'
        };
    }
  };

  const config = getConfig();

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        />

        {/* Dialog */}
        <motion.div
          className="relative z-10 w-full max-w-md mx-4"
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
        >
          <MagicCard 
            className="p-6 bg-white dark:bg-gray-900"
            gradientColor={config.gradientColor}
            gradientSize={300}
          >
            <div className="text-center space-y-4">
              <motion.div 
                className="text-4xl"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {config.icon}
              </motion.div>
              
              <div>
                <TextAnimate
                  animation="blurInUp"
                  className="text-lg font-semibold text-gray-900 dark:text-white"
                >
                  {title}
                </TextAnimate>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  {message}
                </p>
              </div>

              <div className="flex space-x-3 pt-4">
                <motion.button
                  onClick={onClose}
                  className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {cancelText}
                </motion.button>
                
                <motion.button
                  onClick={onConfirm}
                  className={`flex-1 px-4 py-2 text-sm font-medium text-white ${config.confirmColor} rounded-lg transition-colors`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {confirmText}
                </motion.button>
              </div>
            </div>
          </MagicCard>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

ConfirmDialog.displayName = 'ConfirmDialog';

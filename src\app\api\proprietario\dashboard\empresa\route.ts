import { NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    console.log('🔍 API dashboard/empresa: Iniciando verificação de autenticação...');

    // Usar método padrão do Supabase para verificar autenticação
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    console.log('🔍 API dashboard/empresa: Resultado da verificação:', {
      hasUser: !!user,
      userId: user?.id,
      userRole: user?.user_metadata?.role,
      authError: authError?.message
    });

    if (authError || !user) {
      console.error('❌ Erro de autenticação na API dashboard/empresa:', authError);
      return NextResponse.json(
        { success: false, error: 'Usu<PERSON>rio não autenticado', debug: { authError: authError?.message } },
        { status: 401 }
      );
    }

    const userId = user.id;
    console.log('✅ Usuário autenticado:', userId);

    // Usar cliente administrativo para buscar dados (evita problemas de RLS)
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        cnpj,
        endereco,
        numero,
        complemento,
        bairro,
        cidade,
        estado,
        cep,
        telefone,
        segmento,
        descricao,
        logo_url,
        slug,
        status,
        horario_funcionamento,
        stripe_customer_id,
        created_at,
        updated_at
      `)
      .eq('proprietario_user_id', userId)
      .single();

    if (empresaError && empresaError.code !== 'PGRST116') {
      throw new Error('Erro ao buscar dados da empresa');
    }

    // Se não tem empresa, retornar dados vazios
    if (!empresa) {
      return NextResponse.json({
        success: true,
        data: {
          empresa: null,
          planoSaas: null,
          metricas: null,
          statusConfiguracao: {
            empresa_configurada: false,
            stripe_configurado: false,
            servicos_cadastrados: false,
            horarios_definidos: false,
            colaboradores_ativos: false,
            percentual_conclusao: 0,
            proximos_passos: ['Criar empresa']
          }
        }
      });
    }

    // Buscar plano SaaS
    const { data: assinaturaSaas } = await supabaseAdmin
      .from('assinaturas_saas_empresas')
      .select(`
        planos_saas (
          plano_saas_id,
          nome_plano,
          preco_mensal,
          limite_servicos,
          limite_colaboradores_extras,
          detalhes_funcionalidades
        ),
        status_assinatura,
        data_inicio,
        data_fim,
        stripe_subscription_id
      `)
      .eq('empresa_id', empresa.empresa_id)
      .eq('status_assinatura', 'ativa')
      .single();

    // Buscar métricas do mês atual
    const inicioMes = new Date();
    inicioMes.setDate(1);
    inicioMes.setHours(0, 0, 0, 0);

    const fimMes = new Date();
    fimMes.setMonth(fimMes.getMonth() + 1);
    fimMes.setDate(0);
    fimMes.setHours(23, 59, 59, 999);

    // Agendamentos do mês
    const { data: agendamentosMes } = await supabaseAdmin
      .from('agendamentos')
      .select('status_agendamento, valor_total, created_at')
      .eq('empresa_id', empresa.empresa_id)
      .gte('created_at', inicioMes.toISOString())
      .lte('created_at', fimMes.toISOString());

    // Agendamentos pendentes
    const { data: agendamentosPendentes } = await supabaseAdmin
      .from('agendamentos')
      .select('agendamento_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('status_agendamento', 'Pendente');

    // Clientes únicos do mês
    const { data: clientesUnicos } = await supabaseAdmin
      .from('agendamentos')
      .select('cliente_user_id')
      .eq('empresa_id', empresa.empresa_id)
      .gte('created_at', inicioMes.toISOString())
      .lte('created_at', fimMes.toISOString());

    // Serviços ativos
    const { data: servicosAtivos } = await supabaseAdmin
      .from('servicos')
      .select('servico_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true);

    // Colaboradores ativos
    const { data: colaboradoresAtivos } = await supabaseAdmin
      .from('colaboradores_empresa')
      .select('associacao_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true)
      .eq('convite_aceito', true);

    // Calcular métricas
    const totalAgendamentosMes = agendamentosMes?.length || 0;
    const agendamentosConfirmados = agendamentosMes?.filter(a => a.status_agendamento === 'Confirmado').length || 0;
    const receitaBrutaMes = agendamentosMes?.reduce((sum, a) => sum + (a.valor_total || 0), 0) || 0;
    const taxaConfirmacao = totalAgendamentosMes > 0 ? (agendamentosConfirmados / totalAgendamentosMes) * 100 : 0;
    
    const clientesAtivos = new Set(clientesUnicos?.map(c => c.cliente_user_id)).size;

    // Status de configuração
    const temServicos = (servicosAtivos?.length || 0) > 0;
    const temColaboradores = (colaboradoresAtivos?.length || 0) > 0;
    const temHorarios = !!empresa.horario_funcionamento;
    const temStripe = !!empresa.stripe_customer_id;

    const statusConfiguracao = {
      empresa_configurada: true,
      stripe_configurado: temStripe,
      servicos_cadastrados: temServicos,
      horarios_definidos: temHorarios,
      colaboradores_ativos: temColaboradores,
      percentual_conclusao: [true, temStripe, temServicos, temHorarios, temColaboradores].filter(Boolean).length * 20,
      proximos_passos: [
        ...(!temStripe ? ['Configurar Stripe Connect'] : []),
        ...(!temServicos ? ['Cadastrar serviços'] : []),
        ...(!temHorarios ? ['Definir horários de funcionamento'] : []),
        ...(!temColaboradores ? ['Adicionar colaboradores'] : [])
      ]
    };

    const metricas = {
      total_agendamentos_mes: totalAgendamentosMes,
      receita_bruta_mes: receitaBrutaMes,
      receita_liquida_mes: receitaBrutaMes * (1 - (5 / 100)), // 5% de comissão padrão
      total_clientes_ativos: clientesAtivos,
      total_servicos_ativos: servicosAtivos?.length || 0,
      total_colaboradores_ativos: colaboradoresAtivos?.length || 0,
      taxa_confirmacao_mes: taxaConfirmacao,
      crescimento_receita_percentual: 0, // TODO: Calcular comparando com mês anterior
      agendamentos_pendentes: agendamentosPendentes?.length || 0,
      proximos_vencimentos: 0 // TODO: Implementar lógica de vencimentos
    };

    return NextResponse.json({
      success: true,
      data: {
        empresa,
        planoSaas: assinaturaSaas ? {
          plano_id: (assinaturaSaas as any).planos_saas?.plano_saas_id,
          nome_plano: (assinaturaSaas as any).planos_saas?.nome_plano,
          preco_mensal: (assinaturaSaas as any).planos_saas?.preco_mensal,
          limite_servicos: (assinaturaSaas as any).planos_saas?.limite_servicos,
          limite_colaboradores: (assinaturaSaas as any).planos_saas?.limite_colaboradores_extras,
          recursos_premium: (assinaturaSaas as any).planos_saas?.detalhes_funcionalidades?.premium || false,
          status_assinatura: (assinaturaSaas as any).status_assinatura,
          data_inicio: (assinaturaSaas as any).data_inicio,
          data_fim: (assinaturaSaas as any).data_fim,
          stripe_subscription_id: (assinaturaSaas as any).stripe_subscription_id
        } : null,
        metricas,
        statusConfiguracao
      }
    });

  } catch (error: any) {
    console.error('Erro na API de dados da empresa:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
